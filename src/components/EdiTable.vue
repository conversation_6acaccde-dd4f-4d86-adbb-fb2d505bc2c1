<script setup>
import { createVNode, defineProps, getCurrentInstance, ref } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import jquery from 'jquery'
import DataTable from '@/components/DataTable.vue'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const props = defineProps({
  // 自定义属性
  // 映射器
  mapper: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      path: null,
      idField: 'id'
    }
  },
  actions: {
    type: Array,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  },
  toolbar: {
    type: Array,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  },
  // 新增
  addable: {
    type: Boolean,
    default: false
  },
  // 保存
  edit: {
    type: Function,
    default: null
  },
  // 删除
  remove: {
    type: Function,
    default: null
  }
})

const _actions = []

// 设置编辑按钮
if (props.edit !== false) {
  const _func = jquery.isFunction(props.edit)
    ? props.edit
    : props.edit && jquery.isFunction(props.edit.callback)
      ? props.edit.callback
      : record => {
        const _path = `${props.mapper.path || proxy.$router.currentRoute.value.path}/edit`

        proxy.$router.push({
          path: _path,
          query: {
            id: record[props.mapper.idField]
          }
        })
      }

  _actions.push({
    title: props.edit && typeof props.edit.title === 'string'
      ? props.edit.title
      : '编辑',
    icon: props.edit && typeof props.edit.icon === 'string'
      ? props.edit.icon
      : 'EditOutlined',
    callback (record) {
      _func(record)
    }
  })
}

// 设置删除按钮
if (props.remove !== false) {
  const _func = jquery.isFunction(props.remove)
    ? props.remove
    : props.remove && jquery.isFunction(props.remove.callback)
      ? props.remove.callback
      : null

  if (_func !== null) {
    const _button = {
      title: props.remove && typeof props.remove.title === 'string'
        ? props.remove.title
        : '删除',
      icon: props.remove && typeof props.remove.icon === 'string'
        ? props.remove.icon
        : 'DeleteOutlined',
      danger: true,
      callback (record) {
        return new Promise(resolve => {
          FeedbackUtil.modal('您即将删除该记录，是否继续？', 'confirm', {
            icon: createVNode(ExclamationCircleOutlined),
            onOk () {
              _func(record).then(() => {
                resolve()

                table.value.load()
              })

              return Promise.resolve()
            },
            onCancel () {
              resolve()
            }
          })
        })
      }
    }

    _actions.push(_button)
  }
}

// 设置其它按钮
if (Array.isArray(props.actions)) {
  props.actions.forEach(i => {
    _actions.push(i)
  })
}

// 设置工具栏
const _toolbar = []
if (props.addable === true) {
  _toolbar.push({
    title: '新增',
    icon: 'PlusOutlined',
    callback () {
      proxy.$router.push({
        path: `${props.mapper.path || proxy.$router.currentRoute.value.path}/edit`
      })
    }
  })
}
if (Array.isArray(props.toolbar)) {
  props.toolbar.forEach(i => {
    _toolbar.push(i)
  })
}

const table = ref()

defineExpose({
  load (page) {
    table.value.load(page)
  },
  getCurrentPageIndex () {
    table.value.getCurrentPageIndex()
  }
})
</script>

<template>
  <DataTable
    ref="table"
    v-bind="$attrs"
    :actions="_actions"
    :toolbar="_toolbar"
  />
</template>
