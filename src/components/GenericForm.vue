<script setup>
import { getCurrentInstance, ref } from 'vue'
import dayjs from 'dayjs'
import jquery from 'jquery'
import Editor from '@/components/Editor.vue'
import FileUploader from '@/components/FileUploader.vue'
import LocationPicker from '@/components/LocationPicker.vue'
import OrganizationPicker from '@/components/OrganizationPicker.vue'
import StatefulButton from '@/components/StatefulButton.vue'
import MediaApi from '@/api/media/record.js'

const { proxy } = getCurrentInstance()

const props = defineProps({
  fields: {
    type: Array,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  },
  actions: {
    type: Array,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  },
  labelCol: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      sm: {
        span: 7
      },
      lg: {
        span: 7
      }
    }
  },
  wrapperCol: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      sm: {
        span: 17
      },
      lg: {
        span: 10
      }
    }
  }
})

// 标签后面不使用冒号
const colon = false

// 操作响应式偏移
const actionCol = {}
for (const prop in props.labelCol) {
  actionCol[prop] = {
    offset: props.labelCol[prop].span
  }
}

const fields = ref([])
const _model = {}

// 初始化各字段配置项
props.fields.forEach(i => {
  const _config = {
    rules: []
  }

  switch (i.type) {
    case 'datetime':
      _config.format = 'YYYY-MM-DD HH:mm:ss'
      _config.range = false
      _config.showTime = {
        format: 'HH:mm:ss'
      }
      _config.placeholder = ''

      break
    case 'editor':
      _config.height = '600px'

      _config.upload = (type, file) => {
        return new Promise((resolve, reject) => {
          MediaApi.uploadForm(null, [file])
            .then(result => {
              if (Array.isArray(result.data) && result.data.length > 0) {
                if (result.data[0].success) {
                  const _data = {
                    id: result.data[0].id
                  }

                  switch (type) {
                    case 'attachment':
                      _data.fileName = result.data[0].name
                      _data.href = MediaApi.preview(result.data[0].id)

                      break
                    case 'image':
                      _data.src = MediaApi.preview(result.data[0].id)
                      _data.alt = ''

                      break
                    case 'video':
                      _data.src = MediaApi.preview(result.data[0].id)
                      _data.poster = ''

                      break
                  }

                  resolve({
                    errorCode: 0,
                    data: _data
                  })
                } else {
                  // eslint-disable-next-line prefer-promise-reject-errors
                  reject({
                    errorCode: 1,
                    message: result.data[0].message
                  })
                }
              } else {
                // eslint-disable-next-line prefer-promise-reject-errors
                reject({
                  errorCode: 2,
                  message: '上传失败'
                })
              }
            })
            .catch(result => {
              // eslint-disable-next-line prefer-promise-reject-errors
              reject({
                errorCode: result.code,
                message: result.message
              })
            })
        })
      }

      _config.onChange = (html, text) => {
        model.value[i.field] = text ? html : null
      }

      break
    case 'file':
      _config.accept = '*'
      _config.component = 'dragger'
      _config.label = '上传'
      _config.max = 2

      _config.upload = file => {
        return new Promise((resolve, reject) => {
          MediaApi.uploadForm(null, [file])
            .then(result => {
              if (Array.isArray(result.data) && result.data.length > 0) {
                resolve({
                  id: result.data[0].id,
                  url: MediaApi.preview(result.data[0].id)
                })
              } else {
                // eslint-disable-next-line prefer-promise-reject-errors
                reject({
                  message: '上传失败'
                })
              }
            })
            .catch(result => {
              reject(result)
            })
        })
      }

      _config.onChange = files => {
        model.value[i.field] = files.length === 0
          ? null
          : {
              ids: files.map(i => i._id),
              files
            }

        // 触发验证
        proxy.$refs[`form-item-file-uploader-${i.field}`][0].onFieldChange()
        // form.value.validate([i.field])
      }

      _config.onDownload = file => {
        MediaApi.download(file._id)
      }

      break
    case 'location':
      _config.supportInput = false

      break
    case 'number':
      _config.min = null
      _config.max = null

      break
    case 'organization':
      _config.type = 'all'

      break
    case 'rank':
      _config.count = 5
      _config.allowHalf = false

      break
    case 'select':
      _config.mode = ''
    case 'checkbox':
    case 'radio':
    case 'slider':
      _config.options = []

      break
    case 'textarea':
    default:
      _config.convertEmptyToNull = true
      _config.trim = true

      break
  }

  jquery.extend(true, _config, i.config)

  i.config = _config
  fields.value.push(i)

  // 设置默认值
  switch (i.type) {
    case 'datetime':
      _model[i.field] = i.config.range ? [] : null

      break
    case 'organization':
      _model[i.field] = []

      break
    default:
      _model[i.field] = null

      break
  }
})

const model = ref(_model)
const setModel = data => {
  const _data = {
    ...data
  }

  // 转换日期
  fields.value
    .filter(i => {
      return i.type === 'datetime'
    })
    .forEach(i => {
      if (i.config.range) {
        if (Array.isArray(_data[i.field])) {
          const _array = []
          if (_data[i.field].length > 0) {
            _array.push(_data[i.field][0] === null ? null : dayjs(_data[i.field][0], i.config.format))
          }
          if (_data[i.field].length > 1) {
            _array.push(_data[i.field][1] === null ? null : dayjs(_data[i.field][1], i.config.format))
          }

          _data[i.field] = _array
        } else {
          data[i.field] = []
        }
      } else {
        _data[i.field] = _data[i.field] === null ? null : dayjs(_data[i.field], i.config.format)
      }
    })

  model.value = _data

  // 转换富文本
  fields.value
    .filter(i => {
      return i.type === 'editor'
    })
    .forEach(i => {
      proxy.$refs[`editor-${i.field}`][0].setContent(model.value[i.field])
    })

  // 转换文件
  fields.value
    .filter(i => {
      return i.type === 'file'
    })
    .forEach(i => {
      proxy.$refs[`file-uploader-${i.field}`][0].setFiles(model.value[i.field])
    })

  // 转换组织架构
  fields.value
    .filter(i => {
      return i.type === 'organization'
    })
    .forEach(i => {
      if (!Array.isArray(model.value[i.field])) {
        return
      }

      model.value[i.field].forEach(j => {
        let _type

        if (i.config.type === 'all') {
          _type = Object.prototype.hasOwnProperty.call(j, 'code') ? 'department' : 'user'
        } else {
          _type = i.config.type
        }

        proxy.$refs[`organization-picker-${i.field}`][0].select(j, _type)
      })
    })
}

const getValidatedModel = () => {
  return new Promise((resolve, reject) => {
    form.value.validate()
      .then(() => {
        resolve(getModel())
      })
      .catch(errors => {
        reject(errors)
      })
  })
}

// 读取表单值
const getModel = () => {
  const _model = {
    ...model.value
  }

  // 转换数据
  fields.value.forEach(i => {
    switch (i.type) {
      case 'text':
      case 'textarea':
        if (i.config.convertEmptyToNull && _model[i.field] === '') {
          _model[i.field] = null
        }

        if (i.config.trim && _model[i.field] != null) {
          _model[i.field] = _model[i.field].trim()
        }

        break
    }
  })

  return _model
}

defineExpose({
  setModel,
  getValidatedModel,
  getModel
})

// 规范化取值
fields.value.forEach(i => {
  switch (i.type) {
    case 'checkbox':
      if (!Array.isArray(model[i.field])) {
        model[i.field] = []
      }

      break
    case 'organization':
      model[i.field] = []

      break
    case 'select':
      if (i.config.mode === 'multiple' && !Array.isArray(model[i.field])) {
        model[i.field] = []
      }

      break
  }

  if (i.config.promise instanceof Promise) {
    i.config.promise.then(config => {
      if (config) {
        const _config = {
          ...i.config
        }

        jquery.extend(true, _config, config)

        i.config = _config
      }
    })
  }
})

const form = ref()

// eslint-disable-next-line vue/no-setup-props-destructure
const _actions = props.actions
if (_actions.length > 0 && _actions[0] !== null) {
  const _func = _actions[0].callback

  _actions[0].callback = model => {
    if (model instanceof Promise) {
      return new Promise(resolve => {
        model
          .then(temp => {
            _func(temp).finally(() => {
              resolve()
            })
          })
          .finally(() => {
            resolve()
          })
      })
    } else {
      return new Promise(resolve => {
        _func(model).finally(() => resolve())
      })
    }
  }
}

const actions = ref(_actions)

const pickLocation = field => {
  proxy.$refs[`location-picker-${field.field}`][0].show()
}

const removeLocation = field => {
  model.value[field] = null

  // 触发验证
  proxy.$refs[`form-item-location-${field}`][0].onFieldChange()
}

const locationPicked = (poi, field) => {
  if (poi !== null) {
    model.value[field] = poi.address
  }

  // 触发验证
  proxy.$refs[`form-item-location-${field}`][0].onFieldChange()
}

const pickOrganization = field => {
  proxy.$refs[`organization-picker-${field.field}`][0].show()
}

const removeOrganization = field => {
  model.value[field] = []

  // 触发验证
  proxy.$refs[`form-item-organization-${field}`][0].onFieldChange()
}

const organizationPicked = (departments, users, field) => {
  fields.value
    .filter(i => {
      return i.field === field
    })
    .forEach(i => {
      switch (i.config.type) {
        case 'department':
          model.value[field] = departments
          break
        case 'user':
          model.value[fields] = users
          break
        default:
          model.value[fields] = [].concat(departments).concat(users)
          break
      }
    })

  // 触发验证
  proxy.$refs[`form-item-organization-${field}`][0].onFieldChange()
}
</script>

<template>
  <a-form
    ref="form"
    v-bind="$attrs"
    :label-col="labelCol"
    :model="model"
    :wrapper-col="wrapperCol"
  >
    <template
      v-for="i in fields"
      :key="i.field"
    >
      <!-- 多选框 -->
      <template v-if="i.type === 'checkbox'">
        <a-form-item
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <a-checkbox-group
            v-model:value="model[i.field]"
            :disabled="i.config.disabled"
            :options="i.config.options"
          />
        </a-form-item>
      </template>

      <!-- 日期选择 -->
      <template v-else-if="i.type === 'datetime'">
        <a-form-item
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <template v-if="i.config.range">
            <a-range-picker
              v-model:value="model[i.field]"
              v-bind="i.config"
              :disabled="i.config.disabled"
              style="width: 100%"
            />
          </template>
          <template v-else>
            <a-date-picker
              v-model:value="model[i.field]"
              v-bind="i.config"
              :disabled="i.config.disabled"
              style="width: 100%"
            />
          </template>
        </a-form-item>
      </template>

      <!-- 富文本编辑器 -->
      <template v-else-if="i.type === 'editor'">
        <a-form-item
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <Editor
            :ref="`editor-${i.field}`"
            v-bind="i.config"
          />
        </a-form-item>
      </template>

      <!-- 文件上传 -->
      <template v-else-if="i.type === 'file'">
        <a-form-item
          :ref="`form-item-file-uploader-${i.field}`"
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <FileUploader
            :ref="`file-uploader-${i.field}`"
            :accept="i.config.accept"
            :component="i.config.component"
            :disabled="i.config.disabled"
            :label="i.config.label"
            :max="i.config.max"
            :show-upload-list="i.config.showUploadList"
            :upload="i.config.upload"
            @change="i.config.onChange"
            @download="i.config.onDownload"
          />
        </a-form-item>
      </template>

      <!-- 标签 -->
      <template v-else-if="i.type === 'label'">
        <a-form-item
          :colon="colon"
          :label="i.title"
        >
          <a-input
            v-model:value="model[i.field]"
            :disabled="true"
          />
        </a-form-item>
      </template>

      <!-- 地址 -->
      <template v-else-if="i.type === 'location'">
        <a-form-item
          :ref="`form-item-location-${i.field}`"
          :auto-link="false"
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <template v-if="i.config.supportInput">
            <a-input
              v-model:value="model[i.field]"
              :allow-clear="true"
              :disabled="i.config.disabled"
              @blur="$refs[`form-item-location-${i.field}`][0].onFieldBlur()"
              @change="$refs[`form-item-location-${i.field}`][0].onFieldChange()"
            >
              <template
                v-if="!i.config.disabled"
                #addonAfter
              >
                <environment-outlined
                  style="cursor: pointer"
                  @click="pickLocation(i)"
                />
              </template>
            </a-input>
          </template>
          <template v-else>
            <a-input
              :disabled="true"
              :value="model[i.field]"
            >
              <!-- 清空 -->
              <template
                v-if="!i.config.disabled && model[i.field] !== null"
                #suffix
              >
                <close-circle-filled
                  class="ant-input-clear-icon"
                  @click="removeLocation(i.field)"
                />
              </template>

              <!-- 选择器 -->
              <template
                v-if="!i.config.disabled"
                #addonAfter
              >
                <environment-outlined
                  style="cursor: pointer"
                  @click="pickLocation(i)"
                />
              </template>
            </a-input>
          </template>

          <LocationPicker
            :id="i.field"
            :ref="`location-picker-${i.field}`"
            @picked="locationPicked"
          />
        </a-form-item>
      </template>

      <!-- 数字 -->
      <template v-else-if="i.type === 'number'">
        <a-form-item
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <a-input-number
            v-model:value="model[i.field]"
            :disabled="i.config.disabled"
            :max="i.config.max"
            :min="i.config.min"
          />
        </a-form-item>
      </template>

      <!-- 组织架构 -->
      <template v-else-if="i.type === 'organization'">
        <a-form-item
          :ref="`form-item-organization-${i.field}`"
          :auto-link="false"
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <a-input
            :disabled="true"
            :value="Array.isArray(model[i.field]) ? model[i.field].map(j => j.name).join(', ') : null"
          >
            <!-- 清空 -->
            <template
              v-if="!i.config.disabled && Array.isArray(model[i.field]) && model[i.field].length > 0"
              #suffix
            >
              <close-circle-filled
                class="ant-input-clear-icon"
                @click="removeOrganization(i.field)"
              />
            </template>

            <!-- 选择器 -->
            <template
              v-if="!i.config.disabled"
              #addonAfter
            >
              <apartment-outlined
                style="cursor: pointer"
                @click="pickOrganization(i)"
              />
            </template>
          </a-input>

          <OrganizationPicker
            v-bind="i.config"
            :id="i.field"
            :ref="`organization-picker-${i.field}`"
            @picked="organizationPicked"
          />
        </a-form-item>
      </template>

      <!-- 单选按钮 -->
      <template v-else-if="i.type === 'radio'">
        <a-form-item
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <a-radio-group
            v-model:value="model[i.field]"
            :disabled="i.config.disabled"
            :options="i.config.options"
          />
        </a-form-item>
      </template>

      <!-- 打分 -->
      <template v-else-if="i.type === 'rank'">
        <a-form-item
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <a-rate
            v-model:value="model[i.field]"
            :allow-half="i.config.allowHalf"
            :count="i.config.count"
            :disabled="i.config.disabled"
          />
        </a-form-item>
      </template>

      <!-- 下拉选框 -->
      <template v-else-if="i.type === 'select'">
        <a-form-item
          :colon="colon"
          :has-feedback="true"
          :label="i.title"
          :mode="i.config.mode"
          :name="i.field"
          :rules="i.config.rules"
        >
          <a-select
            v-model:value="model[i.field]"
            :allow-clear="true"
            :disabled="i.config.disabled"
            :options="i.config.options"
            :placeholder="'请选择' + i.title"
          />
        </a-form-item>
      </template>

      <!-- 滑块 -->
      <template v-else-if="i.type === 'slider'">
        <a-form-item
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <a-slider
            v-model:value="model[i.field]"
            :disabled="i.config.disabled"
            :marks="i.path.options"
          />
        </a-form-item>
      </template>

      <!-- 开关 -->
      <template v-else-if="i.type === '_switch'">
        <a-form-item
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <a-switch
            v-model:checked="model[i.field]"
            :disabled="i.config.disabled"
          />
        </a-form-item>
      </template>

      <!-- 多行文本 -->
      <template v-else-if="i.type === 'textarea'">
        <a-form-item
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <a-textarea
            v-model:value="model[i.field]"
            :allow-clear="true"
            :disabled="i.config.disabled"
            :rows="i.config && Number.isInteger(i.config.rows) || 3"
          />
        </a-form-item>
      </template>

      <!-- 默认为单行文本 -->
      <template v-else>
        <a-form-item
          :colon="colon"
          :label="i.title"
          :name="i.field"
          :rules="i.config.rules"
        >
          <a-input
            v-model:value="model[i.field]"
            :allow-clear="true"
            :disabled="i.config.disabled"
          />
        </a-form-item>
      </template>
    </template>

    <a-form-item :wrapper-col="actionCol">
      <a-space>
        <template
          v-for="(i, index) in actions"
          :key="index"
        >
          <!-- 保存 -->
          <template v-if="index === 0 && i !== null">
            <StatefulButton
              :icon="i.icon || 'SaveOutlined'"
              :type="'primary'"
              :on-click="() => i.callback(getValidatedModel())"
            >
              {{ i.title || '保存' }}
            </StatefulButton>
          </template>

          <!-- 删除 -->
          <template v-else-if="index === 1 && i !== null">
            <StatefulButton
              :danger="true"
              :icon="i.icon || 'DeleteOutlined'"
              :type="'primary'"
              :on-click="() => i.callback(getModel())"
            >
              {{ i.title || '删除' }}
            </StatefulButton>
          </template>

          <!-- 其它 -->
          <template v-else-if="i !== null">
            <StatefulButton
              :icon="i.icon"
              :on-click="() => i.callback(getModel())"
            >
              {{ i.title }}
            </StatefulButton>
          </template>
        </template>
      </a-space>
    </a-form-item>
  </a-form>
</template>

<style lang="less" scoped>
:deep(textarea) {
  resize: none;
}

.ant-input-affix-wrapper {
  border-radius: 6px;
}

.ant-input-affix-wrapper-lg {
  border-radius: 8px;
}

.ant-input-number-in-form-item {
  width: 100%;
}
</style>
