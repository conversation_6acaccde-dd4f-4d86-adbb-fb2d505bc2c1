<script setup>
import { getCurrentInstance, onMounted, ref } from 'vue'
import ApplicationApi from '@/api/llm/application.js'

const { proxy } = getCurrentInstance()

const records = ref([])

const navigateToEdit = id => {
  proxy.$router.push({
    name: 'llm.application.edit',
    query: id
      ? {
          id
        }
      : {}
  })
}

onMounted(() => {
  ApplicationApi.me({
    toast: {
      success: false
    }
  }).then(result => {
    records.value = result.data
  })
})
</script>

<template>
  <div style="padding: 24px;">
    <!-- 新建应用卡片 -->
    <a-card
      :hoverable="true"
      style="margin-bottom: 16px; cursor: pointer;"
      @click="navigateToEdit()"
    >
      <a-card-meta
        title="创建新应用"
        description="点击创建一个新的LLM应用"
      >
        <template #avatar>
          <a-avatar style="background-color: #1890ff;">
            <plus-outlined />
          </a-avatar>
        </template>
      </a-card-meta>
    </a-card>

    <!-- 现有应用列表 -->
    <a-row :gutter="[16, 16]">
      <a-col
        v-for="app in records"
        :key="app.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
      >
        <a-card :hoverable="true">
          <a-card-meta
            :title="app.name || '未命名应用'"
            :description="app.description || '暂无描述'"
          >
            <template #avatar>
              <a-avatar style="background-color: #52c41a;">
                <robot-outlined />
              </a-avatar>
            </template>
          </a-card-meta>

          <template #actions>
            <setting-outlined key="setting" />
            <edit-outlined
              key="edit"
              style="cursor: pointer;"
              @click="navigateToEdit(app.id)"
            />
            <ellipsis-outlined key="ellipsis" />
          </template>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style lang="less" scoped>
</style>
