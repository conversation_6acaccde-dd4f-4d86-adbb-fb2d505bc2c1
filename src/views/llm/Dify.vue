<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  baseURL: {
    type: String,
    default: 'http://localhost'
  },
  getToken: {
    type: Function,
    default: () => {
      Promise.resolve(null)
    }
  }
})

const signInURL = computed(() => {
  return `${props.baseURL}/signin`
})

const appsURL = computed(() => {
  return `${props.baseURL}/apps`
})

const route = ref(appsURL.value)

const showToolbar = ref(false)

const dify = ref()

const load = () => {
  const _window = dify.value.contentWindow
  const _document = _window.document

  // 注入样式，隐藏元素
  const _style = _document.createElement('style')
  _style.textContent = `
        body > div > div > div.basis-auto {
          display: none;
        }

        body > div > div > div.bg-background-body > div.bg-background-body {
          display: none;
        }

        body > div > div > div.bg-background-body > div.content-start > div.group {
          display: none;
        }

        footer {
          display: none;
        }
      `
  _document.head.appendChild(_style)

  // 注入脚本，监听路由变化
  const _script = `
        (function() {
          const pushState = window.history.pushState;
          const replaceState = window.history.replaceState;

          window.history.pushState = function(...args) {
            pushState.apply(window.history, args);
            window.parent.postMessage({
              type: 'dify-route-change',
              url: window.location.href
            }, "*");
          };

          window.history.replaceState = function(...args) {
            replaceState.apply(window.history, args);
            window.parent.postMessage({
              type: "dify-route-change",
              url: window.location.href
            }, "*");
          };

          window.addEventListener("popstate", () => {
            window.parent.postMessage({
              type: "dify-route-change",
              url: window.location.href
            }, "*");
          });
        })();
      `
  _window.eval(_script)

  window.addEventListener('message', event => {
    console.log(event.data.url)

    if (event.data.type === 'dify-route-change') {
      // 应用配置页面
      if (/app\/[0-9a-z\\-]{36}\/configuration/g.test(event.data.url)) {
        showToolbar.value = true
      } else {
        showToolbar.value = false
      }

      // 登录页面
      if (signInURL.value === event.data.url) {
        props.getToken()
          .then(token => {
            // 设置token
            _document.cookie = typeof _document.cookie === 'string' ? `${_document.cookie}; ` : ''
            _document.cookie += 'console_token=' + token

            // 重定向
            _window.location.href = appsURL.value
          })
      }
    }
  })
}
</script>

<template>
  <iframe
    ref="dify"
    :src="route"
    @load="load"
  />

  <div v-if="showToolbar">
    <slot name="toolbar" />
  </div>
</template>

<style scoped lang="less">
iframe {
  border: none;
  height: calc(100vh - 152px);
  width: 100%;
}
</style>
