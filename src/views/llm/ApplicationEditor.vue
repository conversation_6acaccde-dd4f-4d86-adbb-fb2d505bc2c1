<script setup>
import { computed, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const props = defineProps({
  baseURL: {
    type: String,
    default: 'http://localhost'
  },
  getToken: {
    type: Function,
    default: () => {
      Promise.resolve(null)
    }
  }
})

// 获取应用ID（如果有的话）
const applicationId = computed(() => route.query.id)

const signInURL = computed(() => {
  return `${props.baseURL}/signin`
})

const appsURL = computed(() => {
  return `${props.baseURL}/apps`
})

const iframeRoute = computed(() => {
  if (applicationId.value) {
    // 如果有应用ID，导航到编辑页面
    return `${props.baseURL}/app/${applicationId.value}/configuration`
  }
  // 否则显示应用列表
  return appsURL.value
})

const showToolbar = ref(false)

const dify = ref()

const load = () => {
  const _window = dify.value.contentWindow
  const _document = _window.document

  // 注入样式，隐藏元素
  const _style = _document.createElement('style')
  _style.textContent = `
        body > div > div > div.basis-auto {
          display: none;
        }

        body > div > div > div.bg-background-body > div.bg-background-body {
          display: none;
        }

        body > div > div > div.bg-background-body > div.content-start > div.group {
          display: none;
        }

        footer {
          display: none;
        }
      `
  _document.head.appendChild(_style)

  // 注入脚本，监听路由变化
  const _script = `
        (function() {
          const pushState = window.history.pushState;
          const replaceState = window.history.replaceState;

          window.history.pushState = function(...args) {
            pushState.apply(window.history, args);
            window.parent.postMessage({
              type: 'dify-route-change',
              url: window.location.href
            }, "*");
          };

          window.history.replaceState = function(...args) {
            replaceState.apply(window.history, args);
            window.parent.postMessage({
              type: "dify-route-change",
              url: window.location.href
            }, "*");
          };

          window.addEventListener("popstate", () => {
            window.parent.postMessage({
              type: "dify-route-change",
              url: window.location.href
            }, "*");
          });
        })();
      `
  _window.eval(_script)

  window.addEventListener('message', event => {
    console.log(event.data.url)

    if (event.data.type === 'dify-route-change') {
      // 应用配置页面
      if (/app\/[0-9a-z\\-]{36}\/configuration/g.test(event.data.url)) {
        showToolbar.value = true
      } else {
        showToolbar.value = false
      }

      // 登录页面
      if (signInURL.value === event.data.url) {
        props.getToken()
          .then(token => {
            // 设置token
            _document.cookie = typeof _document.cookie === 'string' ? `${_document.cookie}; ` : ''
            _document.cookie += 'console_token=' + token

            // 重定向
            _window.location.href = appsURL.value
          })
      }
    }
  })
}
</script>

<template>
  <div>
    <div v-if="applicationId" style="padding: 16px; background: #f0f2f5; border-bottom: 1px solid #d9d9d9;">
      <a-space>
        <a-button @click="$router.go(-1)" type="default">
          <arrow-left-outlined />
          返回应用列表
        </a-button>
        <span style="color: #666;">编辑应用: {{ applicationId }}</span>
      </a-space>
    </div>

    <iframe
      ref="dify"
      :src="iframeRoute"
      @load="load"
    />

    <div v-if="showToolbar">
      <slot name="toolbar" />
    </div>
  </div>
</template>

<style scoped lang="less">
iframe {
  border: none;
  height: calc(100vh - 200px);
  width: 100%;
}
</style>
