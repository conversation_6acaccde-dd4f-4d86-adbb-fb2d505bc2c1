---
description: 
globs: 
alwaysApply: true
---
# 项目结构

root                          # 项目根目录
├── index.html                # 项目入口 HTML 文件
├── package.json              # 项目依赖与脚本配置
├── vite.config.js            # Vite 构建工具配置
├── .env.development          # 开发环境变量配置
├── .env.production           # 生产环境变量配置
├── src                       # 源码主目录
│   ├── api                   # 后端接口请求封装
│   │   ├── llm               # llm 相关接口
│   │   ├── media             # 文件管理 相关接口
│   │   ├── sec               # 安全管理 相关接口
│   │   └── sys               # 系统管理 相关接口
│   ├── assets                # 静态资源（图片、字体等）
│   ├── components            # 公共/复用 Vue 组件
│   │   └── llm               # llm 相关组件
│   ├── layouts               # 页面布局组件
│   ├── less                  # 全局和局部 less 样式文件
│   ├── router                # 路由配置（基于 vue-router）
│   ├── store                 # 全局状态管理（基于 vuex）
│   ├── utils                 # 工具函数与封装类（如 HttpRequest）
│   └── views                 # 业务页面组件
│       ├── exception         # 异常页面（如 404、403）
│       ├── llm               # llm 业务页面
│       ├── media             # 文件管理 业务页面
│       ├── sec               # 安全管理 业务页面
│       └── sys               # 系统管理 业务页面

├── public                    # 公共资源目录（构建时复制到输出目录）