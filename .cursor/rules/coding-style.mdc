---
description: 
globs: *.js,*.vue
alwaysApply: false
---
# 命名风格

1. 代码中的命名严禁使用拼音与英文混合的方式，更不允许直接使用中文的方式。
**说明**：正确的英文拼写和语法可以让阅读者易于理解，避免歧义。注意，即使纯拼音命名方式也要避免采用。
**正例**：`alibaba`、`taobao`、`youku` 等国际通用的名称，可视同英文
**反例**：`DaZhePromotion`、`getPingfenByName`

2. 类名使用**UpperCamelCase**风格，必须遵从驼峰形式，但以下情形例外：`DO`、`BO`、`DTO`、`VO`、`AO`
**正例**：`MarcoPolo`、`UserDO`、`XmlService`
**反例**：`macroPolo`、`UserDo`、`XMLService`

3. 方法名、参数名、成员变量、局部变量都统一使用**lowerCamelCase** 风格，必须遵从驼峰形式。
**正例**：`localValue`、`getHttpMessage`、`inputUserId`

4. 局部变量必须以`_`开头，其他类型的变量则不允许。
 **正例**：`_name`、`_idNo`

5. 常量命名全部大写，单词间用下划线隔开，力求语义表达完整清楚。
**正例**：`MAX_STOCK_COUNT`
**反例**：`MAX_COUNT`

6. 杜绝完全不规范的缩写，随意缩写严重降低了代码的可阅读性。
**反例**：`AbstractClass`缩写为`AbsClass`

7. 为了达到代码自解释的目标，任何自定义编程元素在命名时，使用尽量完整的单词组合来表达其意。
**正例**：`PullCodeFromRemoteRepository`（从远程仓库拉取代码）
**反例**：`a`、`b`、`c`

8. 对于迭代变量仅使用`i`、`j`、`k`等标识符。

9. 枚举成员名称需要全大写，单词间用下划线隔开。
**说明**：枚举其实就是特殊的常量类，且构造方法被默认强制是私有
**正例**：`SUCCESS`、`UNKOWN_REASON`

# 常量定义

1. 不允许任何魔法值（即未经定义的常量）直接出现在代码中。
**反例**：`String _key = "Id#taobao_" + tradeId;`

2. 尽量不要使用一个常量类维护所有常量，而是按常量功能进行归类，分开维护。



